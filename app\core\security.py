import secrets
from typing import Optional

from fastapi import Request, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.core.config import ADMIN_API_KEY

bearer_scheme = HTTPBearer()

def verify_admin_key(credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme)):
    """
    验证管理接口的Bearer Token。
    """
    if not secrets.compare_digest(credentials.credentials, ADMIN_API_KEY):
        raise HTTPException(status_code=403, detail="无效的鉴权密钥")

def extract_api_key(request: Request) -> Optional[str]:
    """
    从请求头或查询参数中提取API密钥。
    支持 'Authorization: Bearer sk-...'、'x-goog-api-key: sk-...' 和 '...&key=sk-...'。
    """
    # 1. 从 Authorization header 提取
    auth_header = request.headers.get('authorization')
    if auth_header and auth_header.lower().startswith('bearer '):
        token = auth_header.split(' ', 1)[1]
        if token.startswith('sk-'):
            return token

    # 2. 从 x-goog-api-key header 提取
    if (api_key := request.headers.get('x-goog-api-key')) and api_key.startswith('sk-'):
        return api_key

    # 3. 从 key 查询参数提取
    if (query_key := request.query_params.get('key')) and query_key.startswith('sk-'):
        return query_key

    return None