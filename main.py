import asyncio
import uvicorn
import httpx
from fastapi import FastAPI

from app.api.proxy import router as proxy_router
from app.core.config import config_manager
from app.core.logging import logger
from app.db.database import db_manager

# --- 应用初始化 ---
app = FastAPI(
    title="API 代理服务",
    description="一个高效、可扩展的API代理，具有速率限制、密钥管理和动态配置功能。",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# --- 后台任务 ---

async def periodic_key_cache_refresh_task():
    """
    定期从数据库刷新API密钥缓存。
    """
    while True:
        await asyncio.sleep(15)
        if db_manager._pool:
            await db_manager.refresh_keys_cache()

async def periodic_log_cleanup_task():
    """
    定期清理旧的请求日志，防止 request_logs 表无限增长。
    """
    while True:
        # 每小时运行一次清理
        await asyncio.sleep(3600)
        try:
            async with db_manager._pool.acquire() as conn:
                async with conn.cursor() as cur:
                    # 删除超过24小时的日志
                    cleanup_query = "DELETE FROM request_logs WHERE timestamp < NOW() - INTERVAL 1 DAY;"
                    await cur.execute(cleanup_query)
                    if cur.rowcount > 0:
                        logger.info(f"已从 request_logs 清理 {cur.rowcount} 条旧记录。")
        except Exception as e:
            logger.error(f"清理请求日志时发生错误: {e}", exc_info=True)

# --- 应用生命周期事件 ---
@app.on_event("startup")
async def startup_event():
    """
    应用启动时执行的初始化操作。
    """
    # 初始化HTTP客户端并存储在应用状态中
    app.state.http_client = httpx.AsyncClient(
        follow_redirects=True,
        timeout=60.0,
        limits=httpx.Limits(max_connections=200, max_keepalive_connections=50)
    )
    
    # 连接数据库
    await db_manager.connect(config_manager.config['database'])
    
    # 启动后台任务
    asyncio.create_task(periodic_key_cache_refresh_task())
    asyncio.create_task(periodic_log_cleanup_task())
    
    logger.info("服务已启动，后台任务（密钥缓存刷新、日志清理）已运行。")

@app.on_event("shutdown")
async def shutdown_event():
    """
    应用关闭时执行的清理操作。
    """
    if hasattr(app.state, 'http_client'):
        await app.state.http_client.aclose()
        logger.info("全局HTTP客户端已关闭。")
    
    await db_manager.close()
    logger.info("服务已平稳关闭。")

# --- 包含API路由 ---
app.include_router(proxy_router)

# --- 运行应用 ---
if __name__ == "__main__":
    # 注意：在生产环境中，建议使用Gunicorn + Uvicorn workers
    # 例如: gunicorn main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:8333
    uvicorn.run(app, host="0.0.0.0", port=8333)