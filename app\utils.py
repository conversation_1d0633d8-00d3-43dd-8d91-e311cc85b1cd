import shlex
from typing import Dict, Optional

from fastapi import Request

from app.core.logging import logger

def get_client_ip(request: Request) -> str:
    """
    从请求头中获取真实的客户端IP地址。
    依次检查 'cf-connecting-ip', 'x-real-ip', 'x-forwarded-for'。
    """
    headers = ['cf-connecting-ip', 'x-real-ip', 'x-forwarded-for']
    for header in headers:
        if ip := request.headers.get(header):
            # 'x-forwarded-for' 可能包含多个IP，取第一个
            return ip.split(',')[0].strip()
    
    # 如果都找不到，则使用连接的客户端地址
    return request.client.host if request.client else '127.0.0.1'

def get_country_code(request: Request) -> str:
    """
    从Cloudflare的 'cf-ipcountry' 头中获取国家代码。
    """
    return request.headers.get('cf-ipcountry', 'Unknown')

def format_as_curl(method: str, url: str, headers: Dict[str, str], body: Optional[bytes]) -> str:
    """
    将HTTP请求格式化为cURL命令字符串，用于日志记录。
    """
    parts = [f"curl -X {method}"]
    for key, value in headers.items():
        # 使用 shlex.quote 来安全地处理头信息
        parts.append(f"-H {shlex.quote(f'{key}: {value}')}")
    
    if body:
        # 尝试解码为UTF-8，如果失败则替换错误字符
        body_str = body.decode('utf-8', errors='replace')
        parts.append(f"--data-binary {shlex.quote(body_str)}")
    
    # 对URL进行引用
    parts.append(shlex.quote(url))
    
    return " ".join(parts)

def rewrite_path(path: str) -> str:
    """
    根据预设规则重写请求路径，以兼容不同的API版本格式。
    """
    original_path = path
    
    # 规范化路径，移除开头和结尾的斜杠以及重复的斜杠
    path = "/".join(filter(None, path.strip('/').split('/')))
    
    rewritten = True
    # 规则 1: /chat/completions -> /v1/chat/completions
    if path == "chat/completions":
        path = "v1/chat/completions"
    # 规则 2: /v1/v1beta/* -> /v1beta/*
    elif path.startswith("v1/v1beta/"):
        path = path.removeprefix("v1/")
    # 规则 3: /v1beta/v1/* -> /v1/*
    elif path.startswith("v1beta/v1/"):
        path = path.removeprefix("v1beta/")
    # 规则 4: /v1/v1/* -> /v1/*
    elif path.startswith("v1/v1/"):
        path = path.removeprefix("v1/")
    # 规则 5: /v1beta/v1beta/* -> /v1beta/*
    elif path.startswith("v1beta/v1beta/"):
        path = path.removeprefix("v1beta/")
    # 规则 6: 如果路径不是以 v1/ 或 v1beta/ 开头，则自动添加 /v1beta/ 前缀
    elif not (path.startswith("v1/") or path.startswith("v1beta/")):
        path = f"v1beta/{path}"
    else:
        rewritten = False

    if rewritten:
        logger.info(f"路径自动转发: /{original_path} -> /{path}")
        
    return path