import logging
import sys

def setup_logging():
    """
    配置全局日志记录器。
    """
    logger = logging.getLogger("api_proxy")
    logger.setLevel(logging.INFO)

    # 如果已经有处理器，则清除它们以避免重复记录
    if logger.hasHandlers():
        logger.handlers.clear()

    # 创建一个格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建一个控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    # 将处理器添加到记录器
    logger.addHandler(console_handler)

    return logger

# 初始化并获取日志记录器实例
logger = setup_logging()