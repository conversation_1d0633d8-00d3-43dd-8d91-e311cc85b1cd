import os
from typing import Dict, Any
from app.core.logging import logger

# --- 常量 ---
# 从环境变量或提供一个默认值
ADMIN_API_KEY = os.getenv("ADMIN_API_KEY", "sk-JddJ3MuX9XUmoY6wEMwIf4dbDaXUC9DjpeYT1LaVXT9GSeiZ")

class ConfigManager:
    """
    通过环境变量管理应用配置。
    """
    def __init__(self):
        self.config: Dict[str, Any] = {}
        self.load_config()

    def load_config(self):
        """
        从环境变量加载和验证配置。
        """
        try:
            self.config = {
                'target_url': os.getenv("TARGET_URL"),
                'target_api_key': os.getenv("TARGET_API_KEY"),
                'rpm_limit': int(os.getenv("RPM_LIMIT", 0)),
                'daily_requests_limit': int(os.getenv("DAILY_REQUESTS_LIMIT", 0)),
                'daily_data_limit_mb': int(os.getenv("DAILY_DATA_LIMIT_MB", 0)),
                'ip_lock_hours': int(os.getenv("IP_LOCK_HOURS", 0)),
                'ip_lock_max_ips': int(os.getenv("IP_LOCK_MAX_IPS", 0)),
                'database': {
                    'host': os.getenv("DB_HOST"),
                    'port': int(os.getenv("DB_PORT", 3306)),
                    'user': os.getenv("DB_USER"),
                    'password': os.getenv("DB_PASSWORD"),
                    'db': os.getenv("DB_NAME"),
                }
            }
            self._validate_config(self.config)
            logger.info("配置已成功从环境变量加载。")
        except (ValueError, TypeError) as e:
            logger.error(f"配置加载失败: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"加载配置时发生未知错误: {e}", exc_info=True)
            raise

    def _validate_config(self, config: Dict[str, Any]):
        """
        验证从环境变量加载的配置。
        """
        required_fields = [
            'target_url', 'target_api_key', 'rpm_limit', 'daily_requests_limit',
            'daily_data_limit_mb', 'ip_lock_hours', 'ip_lock_max_ips'
        ]
        for field in required_fields:
            if config.get(field) is None:
                raise ValueError(f"必需的环境变量 '{field.upper()}' 未设置。")

        # 验证数据库配置
        db_config = config.get('database', {})
        required_db_fields = ['host', 'user', 'password', 'db']
        for field in required_db_fields:
            if not db_config.get(field):
                raise ValueError(f"必需的数据库环境变量 'DB_{field.upper()}' 未设置。")

    def get(self, key: str, default: Any = None) -> Any:
        """
        安全地从配置中获取一个值。
        """
        return self.config.get(key, default)

# --- 全局配置实例 ---
config_manager = ConfigManager()