import asyncio
import json
import time
from collections import defaultdict
from datetime import datetime

import httpx
from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import StreamingResponse, PlainTextResponse

from app.core.config import config_manager
from app.core.limiter import rate_limiter
from app.core.logging import logger
from app.core.security import extract_api_key, verify_admin_key
from app.db.database import db_manager
from app.models import CreateKeyRequest
from app.utils import get_client_ip, get_country_code, rewrite_path, format_as_curl

router = APIRouter()
key_locks: defaultdict[str, asyncio.Lock] = defaultdict(asyncio.Lock)

# --- 管理接口 ---
@router.post(
    "/admin/createkey",
    response_class=PlainTextResponse,
    dependencies=[Depends(verify_admin_key)],
    tags=["Admin"],
    summary="创建一个新的API密钥"
)
async def create_key(payload: CreateKeyRequest):
    """
    为指定的所有者创建一个新的API密钥。
    需要有效的管理员Bearer Token进行认证。
    """
    logger.info(f"收到创建密钥请求，所有者: {payload.owner}")
    new_api_key = await db_manager.create_api_key(owner=payload.owner)
    return new_api_key

# --- 核心代理路由 ---
@router.api_route("/{path:path}", methods=["GET", "POST", "OPTIONS"])
async def proxy_request(request: Request, path: str):
    """
    核心代理逻辑，处理所有传入的API请求。
    """
    start_time = time.time()
    
    # 1. 提取和验证API密钥
    api_key = extract_api_key(request)
    if not api_key:
        raise HTTPException(status_code=401, detail="未提供有效的API密钥")

    # 2. 检查密钥是否存在（首先检查缓存）
    if api_key not in db_manager.valid_keys_cache:
        key_data = await db_manager.get_key_data(api_key)
        if not key_data:
            raise HTTPException(status_code=403, detail="无效的API密钥")
        db_manager.valid_keys_cache.add(api_key)
    else:
        key_data = await db_manager.get_key_data(api_key)
        if not key_data: # 缓存命中但数据库中已删除
            db_manager.valid_keys_cache.remove(api_key)
            raise HTTPException(status_code=403, detail="无效的API密钥")

    client_ip = get_client_ip(request)
    country = get_country_code(request)
    logger.info(f"收到请求: Key=...{api_key[-4:]}, IP={client_ip}, Country={country}, Path=/{path}")

    # 3. 路径重写
    rewritten_path = rewrite_path(path)
    
    request_body = await request.body()
    request_size = len(request_body) + sum(len(k) + len(v) for k, v in request.headers.items())

    # 4. 速率和IP限制检查 (使用异步锁确保原子性)
    async with key_locks[api_key]:
        # 重新获取最新数据以进行检查
        key_data = await db_manager.get_key_data(api_key)
        if not key_data:
            raise HTTPException(status_code=403, detail="无效的API密钥")

        if rate_limiter.is_locked(key_data):
            logger.warning(f"拒绝锁定请求: Key=...{api_key[-4:]}, IP={client_ip}")
            raise HTTPException(status_code=429, detail="API密钥因异常活动已被临时锁定")

        if not await rate_limiter.check_ip_limit(key_data, client_ip, config_manager.config):
            logger.warning(f"IP超限，锁定密钥: Key=...{api_key[-4:]}, IP={client_ip}")
            raise HTTPException(status_code=429, detail="IP使用超出限制，密钥已被锁定")
        
        is_allowed, reason = await rate_limiter.check_rate_limit(api_key, request_size, key_data, config_manager.config)
        if not is_allowed:
            logger.warning(f"速率限制: Key=...{api_key[-4:]}, IP={client_ip}, 原因: {reason}")
            raise HTTPException(status_code=429, detail=reason)

    # 5. 构建上游请求
    base_url = config_manager.config['target_url'].rstrip('/')
    target_url = f"{base_url}/{rewritten_path}"
    target_api_key = config_manager.config['target_api_key']

    upstream_headers = {k: v for k, v in request.headers.items() if k.lower() not in ['host', 'authorization', 'x-goog-api-key', 'content-length']}
    upstream_headers['Authorization'] = f"Bearer {target_api_key}"
    upstream_headers['x-goog-api-key'] = f"{target_api_key}"
    
    final_params = dict(request.query_params)
    if 'key' in final_params:
        final_params['key'] = target_api_key

    http_client: httpx.AsyncClient = request.app.state.http_client
    upstream_request = http_client.build_request(
        method=request.method,
        url=target_url,
        headers=upstream_headers,
        params=final_params,
        content=request_body,
        timeout=60.0
    )

    # 6. 发送请求到上游并流式传输响应
    try:
        response_stream = await http_client.send(upstream_request, stream=True)
        
        async def stream_generator():
            response_body_chunks = []
            try:
                async for chunk in response_stream.aiter_bytes():
                    response_body_chunks.append(chunk)
                    yield chunk
            except Exception as e:
                logger.error(f"代理流传输中断: {e}", exc_info=True)
            finally:
                await response_stream.aclose()
                full_response_body = b"".join(response_body_chunks)
                elapsed = time.time() - start_time
                
                # 7. 记录上游错误
                if response_stream.status_code >= 400:
                    user_curl = format_as_curl(request.method, str(request.url), dict(request.headers), None)
                    upstream_curl = format_as_curl(upstream_request.method, str(upstream_request.url), dict(upstream_request.headers), None)
                    logger.error(
                        f"上游服务错误: Key=...{api_key[-4:]}, IP={client_ip}, Status={response_stream.status_code}, Time={elapsed:.2f}s\n"
                        f"--- [用户请求CURL] ---\n{user_curl}\n"
                        f"--- [上游请求CURL] ---\n{upstream_curl}\n"
                        f"--- [上游响应头] ---\n{json.dumps(dict(response_stream.headers), indent=2)}\n"
                        f"--- [上游响应体] ---\n{full_response_body.decode(errors='replace')}\n"
                        f"--- [日志结束] ---"
                    )
                else:
                    # 8. 更新密钥使用统计
                    async with key_locks[api_key]:
                        fresh_key_data = await db_manager.get_key_data(api_key)
                        if fresh_key_data:
                            fresh_key_data.total_requests += 1
                            fresh_key_data.total_data += request_size
                            fresh_key_data.last_request_at = datetime.now()
                            if fresh_key_data.ips is None:
                                fresh_key_data.ips = {}
                            fresh_key_data.ips[client_ip] = f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}_{country}"
                            await db_manager.update_key_stats(fresh_key_data)
                    logger.info(f"请求完成: Key=...{api_key[-4:]}, IP={client_ip}, Status={response_stream.status_code}, Time={elapsed:.2f}s")

        response_headers = {k: v for k, v in response_stream.headers.items() if k.lower() not in ['content-encoding', 'content-length', 'transfer-encoding']}
        return StreamingResponse(stream_generator(), status_code=response_stream.status_code, headers=response_headers, media_type=response_stream.headers.get('content-type'))

    except httpx.ConnectError as e:
        user_curl = format_as_curl(request.method, str(request.url), dict(request.headers), None)
        upstream_curl = format_as_curl(upstream_request.method, str(upstream_request.url), dict(upstream_request.headers), None)
        logger.error(
            f"代理请求失败(无法连接到上游服务 {target_url}): IP={client_ip}, Error: {e}\n"
            f"--- [用户请求CURL] ---\n{user_curl}\n"
            f"--- [上游请求CURL] ---\n{upstream_curl}\n"
            f"--- [日志结束] ---",
            exc_info=True
        )
        raise HTTPException(status_code=502, detail=f"无法连接到上游服务: {e}")
    except httpx.RequestError as e:
        user_curl = format_as_curl(request.method, str(request.url), dict(request.headers), None)
        upstream_curl = format_as_curl(upstream_request.method, str(upstream_request.url), dict(upstream_request.headers), None)
        logger.error(
            f"代理请求失败(上游请求错误): IP={client_ip}, Error: {e}\n"
            f"--- [用户请求CURL] ---\n{user_curl}\n"
            f"--- [上游请求CURL] ---\n{upstream_curl}\n"
            f"--- [日志结束] ---",
            exc_info=True
        )
        raise HTTPException(status_code=502, detail=f"上游服务请求失败: {e}")