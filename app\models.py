from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class CreateKeyRequest(BaseModel):
    """
    管理接口创建API密钥的请求体。
    """
    owner: str = Field(..., description="API密钥的所有者标识")

class APIKeyData(BaseModel):
    """
    数据库中API密钥记录的数据模型。
    """
    api_key: str
    owner: str
    total_requests: int = 0
    total_data: int = 0
    ips: Optional[Dict[str, str]] = Field(default_factory=dict)
    locked_until: int = 0
    last_request_at: Optional[datetime] = None
    created_at: datetime

    class Config:
        orm_mode = True