# 第一阶段：构建阶段 (可以省略，如果你的依赖不需要编译，或者你不需要减小镜像大小)
# 通常，对于Python应用，如果依赖不需要复杂编译，直接在最终镜像中安装也可以。
# 但如果你有需要编译的C扩展（如psycopg2, numpy），或者希望保持最终镜像更干净，可以考虑多阶段构建。
# 对于多数简单应用，直接使用单阶段构建可能更简单。

# 这里展示一个简单的单阶段构建，足以满足大部分Flask/Django应用的需求

# 1. 选择基础镜像
# 使用 python:3.13.5-slim-bookworm 作为基础镜像
# 它提供了 Python 3.13.5，基于轻量级的 Debian Bookworm (Debian 12)
FROM python:3.13.5-slim-bookworm

# 2. 设置工作目录
# WORKDIR 命令设置了容器内部的工作目录，后续命令（如COPY, RUN, CMD）都会在这个目录下执行
WORKDIR /app

# 3. 拷贝依赖文件
# 优先拷贝 requirements.txt，这样可以利用 Docker 的缓存机制。
# 如果 requirements.txt 不变，后续的 RUN pip install 命令就不会重复执行，加快构建速度。
COPY requirements.txt .

# 4. 安装 Python 依赖
# 使用 pip 安装依赖。
# --no-cache-dir 参数可以减少 pip 缓存，从而减小最终镜像的大小。
# 如果你的 Python 包需要系统级别的依赖（例如一些图形库），你可能需要在此之前使用 apt-get install 安装。
# 例如： RUN apt-get update && apt-get install -y --no-install-recommends libpq-dev && rm -rf /var/lib/apt/lists/*
# 但对于简单的 Flask/Django 应用，通常不需要额外系统依赖。
RUN pip install --no-cache-dir -r requirements.txt

# 5. 拷贝应用代码
# 将当前目录（宿主机的 your_project/）下的所有文件拷贝到容器的 /app 目录下
COPY . .

# 6. 暴露端口 (可选，但推荐)
# EXPOSE 命令声明容器在运行时监听的端口。这仅仅是一个文档作用，不是强制性的。
# 实际的端口映射是在运行 `docker run` 命令时通过 `-p` 参数完成的。
EXPOSE 8333

# 7. 定义容器启动命令
# CMD 定义了容器启动时要执行的命令。如果 Dockerfile 中有多个 CMD，只有最后一个生效。
# 推荐使用 exec 形式 (方括号)，这样可以更好地处理信号。
# 对于生产环境的 Python Web 应用，推荐使用 Gunicorn 或其他生产级 WSGI 服务器来启动应用。
# 这里的 `app:app` 指的是 `app.py` 文件中的 `app` 变量 (Flask 应用实例)。
CMD ["gunicorn", "main:app", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8333"]